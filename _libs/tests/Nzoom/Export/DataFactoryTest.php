<?php

namespace Tests\Nzoom\Export;

use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\DataFactory;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Provider\ExportTableProviderInterface;
use Nzoom\Export\Provider\ModelTableProvider;

/**
 * Mock User class for testing
 */
class MockUser
{
    private $id;

    public function __construct($id = 123)
    {
        $this->id = $id;
    }

    public function get($key)
    {
        if ($key === 'id') {
            return $this->id;
        }
        return null;
    }
}

/**
 * Test case for DataFactory
 */
class DataFactoryTest extends ExportTestCase
{
    private DataFactory $factory;
    private RegistryMock $registry;
    private MockUser $mockUser;
    private \Outlook $mockOutlook;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../TestHelpers/GlobalMocks.php';

        // Create mock user
        $this->mockUser = new MockUser(123);

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('currentUser', $this->mockUser);

        // Create factory instance
        $this->factory = new DataFactory($this->registry);

        // Create mock outlook
        $this->mockOutlook = new \Outlook();
    }

    public function testConstructor(): void
    {
        $factory = new DataFactory($this->registry);
        $this->assertInstanceOf(DataFactory::class, $factory);
    }

    public function testSetChunkSize(): void
    {
        $this->factory->setChunkSize(100);

        // We can't directly test the chunk size since it's private,
        // but we can test it indirectly through the invoke method
        $this->assertTrue(true); // This test verifies the method exists and doesn't throw
    }

    public function testInvokeWithEmptyModels(): void
    {
        $this->mockOutlook->setFields([]);

        $exportData = $this->factory->__invoke([], $this->mockOutlook);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertEquals(0, $exportData->count());
        $this->assertInstanceOf(ExportHeader::class, $exportData->getHeader());

        // Check metadata
        $metadata = $exportData->getMetadata();
        $this->assertArrayHasKey('generated_at', $metadata);
        $this->assertArrayHasKey('generated_by', $metadata);
        $this->assertInstanceOf(\DateTime::class, $metadata['generated_at']);
        $this->assertEquals(123, $metadata['generated_by']);
    }

    public function testInvokeWithOutlookFields(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'column_width' => 80,
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'column_width' => 200,
                'position' => '2'
            ],
            [
                'name' => 'email',
                'label' => 'Email',
                'field_type' => 'text',
                'position' => '3'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $exportData = $this->factory->__invoke([], $this->mockOutlook);

        $header = $exportData->getHeader();
        $this->assertEquals(3, $header->count());

        // Check first column
        $idColumn = $header->getColumnByVarName('id');
        $this->assertNotNull($idColumn);
        $this->assertEquals('ID', $idColumn->getLabel());
        $this->assertEquals(ExportValue::TYPE_STRING, $idColumn->getType());
        $this->assertEquals(80, $idColumn->getWidth());

        // Check second column
        $nameColumn = $header->getColumnByVarName('name');
        $this->assertNotNull($nameColumn);
        $this->assertEquals('Name', $nameColumn->getLabel());
        $this->assertEquals(ExportValue::TYPE_STRING, $nameColumn->getType());
        $this->assertEquals(200, $nameColumn->getWidth());

        // Check third column (no width specified)
        $emailColumn = $header->getColumnByVarName('email');
        $this->assertNotNull($emailColumn);
        $this->assertEquals('Email', $emailColumn->getLabel());
        $this->assertEquals(ExportValue::TYPE_STRING, $emailColumn->getType());
        $this->assertNull($emailColumn->getWidth());
    }

    public function testInvokeWithModels(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Create mock models
        $model1 = new \Model(
            ['id' => 1],
            ['id' => 1, 'name' => 'John Doe'],
            ['id' => ExportValue::TYPE_STRING, 'name' => ExportValue::TYPE_STRING]
        );

        $model2 = new \Model(
            ['id' => 2],
            ['id' => 2, 'name' => 'Jane Smith'],
            ['id' => ExportValue::TYPE_STRING, 'name' => ExportValue::TYPE_STRING]
        );

        $models = [$model1, $model2];

        $exportData = $this->factory->__invoke($models, $this->mockOutlook);

        $this->assertEquals(2, $exportData->count());

        // Check first record
        $record1 = $exportData->getRecordAt(0);
        $this->assertNotNull($record1);
        $this->assertEquals(1, $record1->getMetadataValue('id'));
        $this->assertEquals(1, $record1->getValueByColumnName('id')->getValue());
        $this->assertEquals('John Doe', $record1->getValueByColumnName('name')->getValue());

        // Check second record
        $record2 = $exportData->getRecordAt(1);
        $this->assertNotNull($record2);
        $this->assertEquals(2, $record2->getMetadataValue('id'));
        $this->assertEquals(2, $record2->getValueByColumnName('id')->getValue());
        $this->assertEquals('Jane Smith', $record2->getValueByColumnName('name')->getValue());
    }

    public function testFieldTypeMapping(): void
    {
        $fieldTypeMappings = [
            'text' => ExportValue::TYPE_STRING,
            'textarea' => ExportValue::TYPE_STRING,
            'dropdown' => ExportValue::TYPE_STRING,
            'radio' => ExportValue::TYPE_STRING,
            'checkbox_group' => ExportValue::TYPE_STRING,
            'date' => ExportValue::TYPE_DATE,
            'datetime' => ExportValue::TYPE_DATETIME,
            'autocompleter' => ExportValue::TYPE_STRING,
            'time' => ExportValue::TYPE_STRING,
        ];

        foreach ($fieldTypeMappings as $fieldType => $expectedExportType) {
            $fields = [
                [
                    'name' => 'test_field',
                    'label' => 'Test Field',
                    'field_type' => $fieldType,
                    'position' => '1'
                ]
            ];

            $this->mockOutlook->setFields($fields);

            $exportData = $this->factory->__invoke([], $this->mockOutlook);
            $header = $exportData->getHeader();
            $column = $header->getColumnByVarName('test_field');

            $this->assertEquals($expectedExportType, $column->getType(),
                "Field type '{$fieldType}' should map to '{$expectedExportType}'");
        }
    }

    public function testUnsupportedFieldTypeThrowsException(): void
    {
        // Use reflection to test the private method directly
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('mapFieldTypeToExportType');
        $method->setAccessible(true);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported field type "unsupported_type". Supported types are: text, textarea, dropdown, radio, checkbox_group, date, datetime, autocompleter, time');

        $method->invoke($this->factory, 'unsupported_type');
    }

    public function testUnsupportedFieldTypeIsSkippedInHeader(): void
    {
        // Test that unsupported field types are gracefully skipped during header creation
        $fields = [
            [
                'name' => 'valid_field',
                'label' => 'Valid Field',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'unsupported_field',
                'label' => 'Unsupported Field',
                'field_type' => 'unsupported_type',
                'position' => '2'
            ],
            [
                'name' => 'another_valid_field',
                'label' => 'Another Valid Field',
                'field_type' => 'date',
                'position' => '3'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $exportData = $this->factory->__invoke([], $this->mockOutlook);
        $header = $exportData->getHeader();

        // Should only have 2 columns (unsupported field should be skipped)
        $this->assertEquals(2, $header->count());
        $this->assertTrue($header->hasColumn('valid_field'));
        $this->assertFalse($header->hasColumn('unsupported_field'));
        $this->assertTrue($header->hasColumn('another_valid_field'));
    }

    public function testInvokeWithLargeModelSet(): void
    {
        // Test chunking by setting a small chunk size
        $this->factory->setChunkSize(2);

        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Create 5 mock models to test chunking (chunk size = 2)
        $models = [];
        for ($i = 1; $i <= 5; $i++) {
            $model = new \Model(
                ['id' => $i],
                ['id' => $i],
                ['id' => ExportValue::TYPE_STRING]
            );
            $models[] = $model;
        }

        $exportData = $this->factory->__invoke($models, $this->mockOutlook);

        $this->assertEquals(5, $exportData->count());

        // Verify all records were processed correctly
        for ($i = 0; $i < 5; $i++) {
            $record = $exportData->getRecordAt($i);
            $this->assertEquals($i + 1, $record->getValueByColumnName('id')->getValue());
        }
    }

    public function testCreateStreaming(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];
        $pageSize = 100;

        $exportData = $this->factory->createStreaming('TestFactory', $filters, $this->mockOutlook, $pageSize);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Check metadata
        $metadata = $exportData->getMetadata();
        $this->assertArrayHasKey('generated_at', $metadata);
        $this->assertArrayHasKey('generated_by', $metadata);
        $this->assertEquals(123, $metadata['generated_by']);
    }

    public function testCreateStreamingWithDefaultPageSize(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];

        // Test with default page size (should be 200)
        $exportData = $this->factory->createStreaming('TestFactory', $filters, $this->mockOutlook);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals(200, $exportData->getPageSize()); // Default page size
    }

    public function testCreateStreamingWithComplexFilters(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $complexFilters = [
            'where' => ['active = 1', 'status != "deleted"'],
            'order' => 'created_at DESC',
            'group' => 'category_id'
        ];

        $exportData = $this->factory->createStreaming('TestFactory', $complexFilters, $this->mockOutlook, 50);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals(50, $exportData->getPageSize());

        // Verify header has correct columns
        $header = $exportData->getHeader();
        $this->assertEquals(2, $header->count());
        $this->assertTrue($header->hasColumn('id'));
        $this->assertTrue($header->hasColumn('name'));
    }

    public function testCreateCursorStreaming(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];
        $pageSize = 1000;
        $cursorField = 'created_at';

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, $cursorField);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Check metadata
        $metadata = $exportData->getMetadata();
        $this->assertArrayHasKey('generated_at', $metadata);
        $this->assertArrayHasKey('generated_by', $metadata);
        $this->assertEquals(123, $metadata['generated_by']);
    }

    public function testCreateCursorStreamingWithDefaultParameters(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];

        // Test with default page size (1000) and cursor field ('id')
        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals(1000, $exportData->getPageSize()); // Default page size
    }

    public function testCreateCursorStreamingWithCustomCursorField(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'timestamp',
                'label' => 'Timestamp',
                'field_type' => 'datetime',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['status = "active"']];
        $pageSize = 500;
        $cursorField = 'timestamp';

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, $cursorField);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Verify header has correct columns
        $header = $exportData->getHeader();
        $this->assertEquals(2, $header->count());
        $this->assertTrue($header->hasColumn('id'));
        $this->assertTrue($header->hasColumn('timestamp'));
    }

    public function testCreateCursorStreamingWithEmptyFilters(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test with empty filters
        $emptyFilters = [];

        $exportData = $this->factory->createCursorStreaming('TestModel', $emptyFilters, $this->mockOutlook);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals(1000, $exportData->getPageSize());
    }

    public function testHeaderCreationWithDefaultValues(): void
    {
        $fields = [
            [
                'name' => 'test_field',
                'label' => 'Test Field',
                'position' => '1'
                // No field_type or column_width specified
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $exportData = $this->factory->__invoke([], $this->mockOutlook);
        $header = $exportData->getHeader();

        $this->assertEquals('f0f0f0', $header->getBackgroundColor());
        $this->assertEquals(['font-weight' => 'bold'], $header->getStyles());

        $column = $header->getColumnByVarName('test_field');
        $this->assertEquals(ExportValue::TYPE_STRING, $column->getType()); // Default type
        $this->assertNull($column->getWidth()); // No width specified
    }

    public function testModelWithoutGetExportVarTypeMethod(): void
    {
        $fields = [
            [
                'name' => 'test_field',
                'label' => 'Test Field',
                'position' => '1',
                'field_type' => 'text'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Create a model without getExportVarType method (empty export types)
        $model = new \Model(
            ['id' => 1],
            ['test_field' => 'test value'],
            [] // No export types defined
        );

        $exportData = $this->factory->__invoke([$model], $this->mockOutlook);

        $this->assertEquals(1, $exportData->count());
        $record = $exportData->getRecordAt(0);
        $value = $record->getValueByColumnName('test_field');

        $this->assertEquals('test value', $value->getValue());
        $this->assertEquals(ExportValue::TYPE_STRING, $value->getType()); // Should use column type
    }

    public function testComplexScenario(): void
    {
        // Test a complex scenario with multiple field types, chunking, and model processing
        $this->factory->setChunkSize(3);

        $fields = [
            ['name' => 'id', 'label' => 'ID', 'field_type' => 'text', 'column_width' => 80, 'position' => '1'],
            ['name' => 'name', 'label' => 'Full Name', 'field_type' => 'text', 'column_width' => 200, 'position' => '2'],
            ['name' => 'salary', 'label' => 'Salary', 'field_type' => 'text', 'position' => '3'],
            ['name' => 'active', 'label' => 'Active', 'field_type' => 'text', 'position' => '4'],
            ['name' => 'created_at', 'label' => 'Created', 'field_type' => 'datetime', 'position' => '5']
        ];

        $this->mockOutlook->setFields($fields);

        // Create 7 models to test chunking (chunk size = 3, so 3 chunks: 3+3+1)
        $models = [];
        for ($i = 1; $i <= 7; $i++) {
            $model = new \Model(
                ['id' => $i],
                [
                    'id' => $i,
                    'name' => "User {$i}",
                    'salary' => 50000 + ($i * 1000),
                    'active' => $i % 2 === 1 ? 'true' : 'false',
                    'created_at' => "2024-01-{$i} 10:00:00"
                ],
                [
                    'id' => ExportValue::TYPE_STRING,
                    'name' => ExportValue::TYPE_STRING,
                    'salary' => ExportValue::TYPE_STRING,
                    'active' => ExportValue::TYPE_STRING,
                    'created_at' => ExportValue::TYPE_DATETIME
                ]
            );
            $models[] = $model;
        }

        $exportData = $this->factory->__invoke($models, $this->mockOutlook);

        // Verify header
        $header = $exportData->getHeader();
        $this->assertEquals(5, $header->count());
        $this->assertEquals('f0f0f0', $header->getBackgroundColor());

        // Verify all records were processed
        $this->assertEquals(7, $exportData->count());

        // Verify specific records
        $record1 = $exportData->getRecordAt(0);
        $this->assertEquals(1, $record1->getValueByColumnName('id')->getValue());
        $this->assertEquals('User 1', $record1->getValueByColumnName('name')->getValue());
        $this->assertEquals(51000, $record1->getValueByColumnName('salary')->getValue());
        $this->assertTrue($record1->getValueByColumnName('active')->getValue());

        $record7 = $exportData->getRecordAt(6);
        $this->assertEquals(7, $record7->getValueByColumnName('id')->getValue());
        $this->assertEquals('User 7', $record7->getValueByColumnName('name')->getValue());
        $this->assertEquals(57000, $record7->getValueByColumnName('salary')->getValue());
        $this->assertTrue($record7->getValueByColumnName('active')->getValue());

        // Verify metadata
        $metadata = $exportData->getMetadata();
        $this->assertInstanceOf(\DateTime::class, $metadata['generated_at']);
        $this->assertEquals(123, $metadata['generated_by']);
    }

    public function testStreamingMethodsCreateProperLazyConfiguration(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test createStreaming
        $streamingData = $this->factory->createStreaming('TestFactory', ['where' => ['active = 1']], $this->mockOutlook, 150);

        $this->assertTrue($streamingData->isLazy());
        $this->assertEquals(150, $streamingData->getPageSize());
        $this->assertInstanceOf(ExportHeader::class, $streamingData->getHeader());
        $this->assertEquals(2, $streamingData->getHeader()->count());

        // Test createCursorStreaming
        $cursorData = $this->factory->createCursorStreaming('TestModel', ['where' => ['status = "active"']], $this->mockOutlook, 250, 'created_at');

        $this->assertTrue($cursorData->isLazy());
        $this->assertEquals(250, $cursorData->getPageSize());
        $this->assertInstanceOf(ExportHeader::class, $cursorData->getHeader());
        $this->assertEquals(2, $cursorData->getHeader()->count());

        // Both should have the same header structure but different lazy configurations
        $this->assertEquals($streamingData->getHeader()->getVarNames(), $cursorData->getHeader()->getVarNames());
    }

    public function testStreamingMethodsWithDifferentFieldTypes(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ],
            [
                'name' => 'salary',
                'label' => 'Salary',
                'field_type' => 'text',
                'position' => '3'
            ],
            [
                'name' => 'active',
                'label' => 'Active',
                'field_type' => 'text',
                'position' => '4'
            ],
            [
                'name' => 'created_at',
                'label' => 'Created At',
                'field_type' => 'datetime',
                'position' => '5'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['department_id = 5']];

        // Test createStreaming with multiple field types
        $exportData = $this->factory->createStreaming('TestFactory', $filters, $this->mockOutlook, 100);

        $header = $exportData->getHeader();
        $this->assertEquals(5, $header->count());

        // Verify field type mappings
        $this->assertEquals(ExportValue::TYPE_STRING, $header->getColumnByVarName('id')->getType());
        $this->assertEquals(ExportValue::TYPE_STRING, $header->getColumnByVarName('name')->getType());
        $this->assertEquals(ExportValue::TYPE_STRING, $header->getColumnByVarName('salary')->getType());
        $this->assertEquals(ExportValue::TYPE_STRING, $header->getColumnByVarName('active')->getType());
        $this->assertEquals(ExportValue::TYPE_DATETIME, $header->getColumnByVarName('created_at')->getType());
    }

    public function testStreamingMethodsPreserveFilterStructure(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test with complex filter structure
        $complexFilters = [
            'where' => ['active = 1', 'department_id IN (1,2,3)'],
            'order' => 'created_at DESC',
            'group' => 'category_id',
            'having' => 'COUNT(*) > 5',
            'join' => 'LEFT JOIN departments ON users.dept_id = departments.id'
        ];

        // Both methods should handle complex filters without modification
        $streamingData = $this->factory->createStreaming('TestFactory', $complexFilters, $this->mockOutlook, 200);
        $cursorData = $this->factory->createCursorStreaming('TestModel', $complexFilters, $this->mockOutlook, 500, 'id');

        $this->assertInstanceOf(ExportData::class, $streamingData);
        $this->assertInstanceOf(ExportData::class, $cursorData);
        $this->assertTrue($streamingData->isLazy());
        $this->assertTrue($cursorData->isLazy());
    }

    public function testCreateStreamingRecordProviderExecution(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];
        $pageSize = 3;

        $exportData = $this->factory->createStreaming('TestFactory', $filters, $this->mockOutlook, $pageSize);

        // Actually trigger the record provider by accessing records
        $records = $exportData->getRecords();

        // Should get records from the mock TestFactory
        $this->assertGreaterThan(0, count($records));
        $this->assertLessThanOrEqual(5, count($records)); // TestFactory returns max 5 records

        // Verify record structure
        if (count($records) > 0) {
            $firstRecord = $records[0];
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportRecord::class, $firstRecord);
            $this->assertTrue($firstRecord->hasValue('id'));
            $this->assertTrue($firstRecord->hasValue('name'));

            // Verify values
            $idValue = $firstRecord->getValueByColumnName('id');
            $nameValue = $firstRecord->getValueByColumnName('name');
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportValue::class, $idValue);
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportValue::class, $nameValue);
            $this->assertIsInt($idValue->getValue());
            $this->assertStringContainsString('User', $nameValue->getValue());
        }
    }

    public function testCreateCursorStreamingRecordProviderExecution(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['status = "active"']];
        $pageSize = 3;

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // Test the streaming configuration without triggering full record loading
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Test individual record access (safer than getRecords() which loads all)
        $firstRecord = $exportData->getRecordAt(0);

        if ($firstRecord !== null) {
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportRecord::class, $firstRecord);
            $this->assertTrue($firstRecord->hasValue('id'));
            $this->assertTrue($firstRecord->hasValue('name'));

            // Verify values
            $idValue = $firstRecord->getValueByColumnName('id');
            $nameValue = $firstRecord->getValueByColumnName('name');
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportValue::class, $idValue);
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportValue::class, $nameValue);
            $this->assertIsInt($idValue->getValue());
            $this->assertStringContainsString('User', $nameValue->getValue());
        }

        // Test that we can access a second record
        $secondRecord = $exportData->getRecordAt(1);
        if ($secondRecord !== null) {
            $this->assertInstanceOf(\Nzoom\Export\Entity\ExportRecord::class, $secondRecord);
            $this->assertTrue($secondRecord->hasValue('id'));
        }
    }

    public function testCreateCursorStreamingWithExistingWhereConditions(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test with existing where conditions
        $filters = [
            'where' => ['status = "active"', 'department_id = 5'],
            'order' => 'created_at DESC'
        ];
        $pageSize = 5;

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // Test individual record access instead of loading all records
        $firstRecord = $exportData->getRecordAt(0);

        // Verify we can get a record
        $this->assertNotNull($firstRecord);

        // Verify the cursor streaming configuration
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Verify header structure
        $header = $exportData->getHeader();
        $this->assertEquals(2, $header->count());
        $this->assertTrue($header->hasColumn('id'));
        $this->assertTrue($header->hasColumn('status'));
    }

    public function testCreateCursorStreamingWithCustomCursorFieldExecution(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'timestamp',
                'label' => 'Timestamp',
                'field_type' => 'datetime',
                'position' => '2'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '3'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = ['where' => ['active = 1']];
        $pageSize = 4;
        $cursorField = 'timestamp';

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, $cursorField);

        // Test individual record access to avoid infinite loop
        $firstRecord = $exportData->getRecordAt(0);

        // Verify record is returned and has correct structure
        $this->assertNotNull($firstRecord);
        $this->assertTrue($firstRecord->hasValue('id'));
        $this->assertTrue($firstRecord->hasValue('timestamp'));
        $this->assertTrue($firstRecord->hasValue('name'));

        // Verify the cursor field value exists
        $timestampValue = $firstRecord->getValueByColumnName('timestamp');
        $this->assertInstanceOf(\Nzoom\Export\Entity\ExportValue::class, $timestampValue);
    }

    public function testCreateCursorStreamingEmptyResultHandling(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Use filters that would result in high cursor values (beyond TestModel's range)
        $filters = [
            'where' => ['id > 15'] // TestModel only goes up to 10
        ];
        $pageSize = 5;

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // The record provider should handle empty results gracefully
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Verify header is still created correctly
        $header = $exportData->getHeader();
        $this->assertEquals(1, $header->count());
        $this->assertTrue($header->hasColumn('id'));
    }

    public function testCreateCursorStreamingWithNoInitialWhereConditions(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test with filters that have no 'where' key initially
        $filters = [
            'order' => 'created_at ASC',
            'limit' => 100
        ];
        $pageSize = 3;

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // Test individual record access
        $firstRecord = $exportData->getRecordAt(0);

        // Should get a record
        $this->assertNotNull($firstRecord);

        // Verify configuration
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // Verify record has expected structure
        $this->assertTrue($firstRecord->hasValue('id'));
        $this->assertTrue($firstRecord->hasValue('name'));

        // First record should have id = 1 (starting from beginning)
        $this->assertEquals(1, $firstRecord->getValueByColumnName('id')->getValue());
    }

    public function testCreateCursorStreamingMultiplePageSimulation(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = [];
        $pageSize = 2; // Small page size to test pagination

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // Test individual record access to verify pagination worked
        $firstRecord = $exportData->getRecordAt(0);
        $secondRecord = $exportData->getRecordAt(1);

        $this->assertNotNull($firstRecord);
        $this->assertNotNull($secondRecord);

        // Verify IDs are present and increasing (cursor pagination may skip values)
        $firstId = $firstRecord->getValueByColumnName('id')->getValue();
        $secondId = $secondRecord->getValueByColumnName('id')->getValue();
        $this->assertGreaterThan(0, $firstId);
        $this->assertGreaterThan($firstId, $secondId);

        // Test accessing a third record
        $thirdRecord = $exportData->getRecordAt(2);
        $this->assertNotNull($thirdRecord);
        $thirdId = $thirdRecord->getValueByColumnName('id')->getValue();
        $this->assertGreaterThan($secondId, $thirdId);
    }

    public function testStreamingPaginationBehavior(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = [];
        $pageSize = 2;

        $exportData = $this->factory->createStreaming('TestFactory', $filters, $this->mockOutlook, $pageSize);

        // Access records to trigger pagination
        $records = $exportData->getRecords();

        // TestFactory should return all available records (up to 5)
        $this->assertGreaterThan(0, count($records));
        $this->assertLessThanOrEqual(5, count($records));

        // Test individual record access
        $firstRecord = $exportData->getRecordAt(0);
        $this->assertNotNull($firstRecord);
        $this->assertEquals(1, $firstRecord->getValueByColumnName('id')->getValue());

        if (count($records) > 1) {
            $secondRecord = $exportData->getRecordAt(1);
            $this->assertNotNull($secondRecord);
            $this->assertEquals(2, $secondRecord->getValueByColumnName('id')->getValue());
        }
    }

    public function testFieldsWithEmptyPositionsAreFiltered(): void
    {
        $fields = [
            [
                'name' => 'visible_field',
                'label' => 'Visible Field',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'zero_position_field',
                'label' => 'Zero Position Field',
                'field_type' => 'text',
                'position' => '0'  // Should be filtered out
            ],
            [
                'name' => 'false_position_field',
                'label' => 'False Position Field',
                'field_type' => 'text',
                'position' => false  // Should be filtered out
            ],
            [
                'name' => 'empty_string_position_field',
                'label' => 'Empty String Position Field',
                'field_type' => 'text',
                'position' => ''  // Should be filtered out
            ],
            [
                'name' => 'no_position_field',
                'label' => 'No Position Field',
                'field_type' => 'text'
                // No position key - should be included (position not set)
            ],
            [
                'name' => 'another_visible_field',
                'label' => 'Another Visible Field',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $exportData = $this->factory->__invoke([], $this->mockOutlook);
        $header = $exportData->getHeader();

        // Should only have 3 columns: visible_field, no_position_field, and another_visible_field
        $this->assertEquals(3, $header->count());

        // Verify the correct fields are included
        $this->assertTrue($header->hasColumn('visible_field'));
        $this->assertTrue($header->hasColumn('no_position_field'));
        $this->assertTrue($header->hasColumn('another_visible_field'));

        // Verify the filtered fields are not included
        $this->assertFalse($header->hasColumn('zero_position_field'));
        $this->assertFalse($header->hasColumn('false_position_field'));
        $this->assertFalse($header->hasColumn('empty_string_position_field'));
    }

    public function testFieldsWithEmptyPositionsInStreamingMethods(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'hidden_field',
                'label' => 'Hidden Field',
                'field_type' => 'text',
                'position' => '0'  // Should be filtered out
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test createStreaming
        $streamingData = $this->factory->createStreaming('TestFactory', [], $this->mockOutlook, 100);
        $streamingHeader = $streamingData->getHeader();

        $this->assertEquals(2, $streamingHeader->count());
        $this->assertTrue($streamingHeader->hasColumn('id'));
        $this->assertTrue($streamingHeader->hasColumn('name'));
        $this->assertFalse($streamingHeader->hasColumn('hidden_field'));

        // Test createCursorStreaming
        $cursorData = $this->factory->createCursorStreaming('TestModel', [], $this->mockOutlook, 100, 'id');
        $cursorHeader = $cursorData->getHeader();

        $this->assertEquals(2, $cursorHeader->count());
        $this->assertTrue($cursorHeader->hasColumn('id'));
        $this->assertTrue($cursorHeader->hasColumn('name'));
        $this->assertFalse($cursorHeader->hasColumn('hidden_field'));
    }

    public function testCursorStreamingPaginationBehavior(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        $filters = [];
        $pageSize = 3;

        $exportData = $this->factory->createCursorStreaming('TestModel', $filters, $this->mockOutlook, $pageSize, 'id');

        // Verify the export data is properly configured
        $this->assertTrue($exportData->isLazy());
        $this->assertEquals($pageSize, $exportData->getPageSize());

        // For cursor streaming, we can't easily test the actual record provider execution
        // because it depends on the static method call which may not work in all test environments
        // Instead, we verify that the streaming configuration is correct
        $header = $exportData->getHeader();
        $this->assertEquals(1, $header->count());
        $this->assertTrue($header->hasColumn('id'));

        // Verify metadata
        $metadata = $exportData->getMetadata();
        $this->assertArrayHasKey('generated_at', $metadata);
        $this->assertArrayHasKey('generated_by', $metadata);
        $this->assertEquals(123, $metadata['generated_by']);
    }

    // ========== TABLE PROVIDER TESTS ==========

    public function testSetTableProvider(): void
    {
        // Initially no table provider
        $this->assertFalse($this->factory->isTablesEnabled());

        // Create a mock table provider
        $mockProvider = $this->createMock(ExportTableProviderInterface::class);

        // Set the table provider
        $this->factory->setTableProvider($mockProvider);

        // Should now be enabled
        $this->assertTrue($this->factory->isTablesEnabled());

        // Set to null to disable
        $this->factory->setTableProvider(null);
        $this->assertFalse($this->factory->isTablesEnabled());
    }

    public function testWithModelTableProvider(): void
    {
        // Initially no table provider
        $this->assertFalse($this->factory->isTablesEnabled());

        // Configure with ModelTableProvider
        $result = $this->factory->withModelTableProvider('full_num', 'Document Number', [
            'include_empty_tables' => true,
            'date_format' => 'd/m/Y'
        ]);

        // Should return self for method chaining
        $this->assertSame($this->factory, $result);

        // Should now be enabled
        $this->assertTrue($this->factory->isTablesEnabled());
    }

    public function testWithModelTableProviderDefaultOptions(): void
    {
        // Test with no options
        $this->factory->withModelTableProvider('full_num', 'Document Number');

        $this->assertTrue($this->factory->isTablesEnabled());
    }

    public function testWithModelTableProviderFluent(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Test fluent interface
        $exportData = $this->factory
            ->withModelTableProvider('full_num', 'Document Number', ['include_empty_tables' => false])
            ->__invoke([], $this->mockOutlook);

        $this->assertInstanceOf(ExportData::class, $exportData);
        $this->assertTrue($this->factory->isTablesEnabled());
    }

    public function testTableProviderWithModels(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ],
            [
                'name' => 'name',
                'label' => 'Name',
                'field_type' => 'text',
                'position' => '2'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Create a mock model with grouping data
        $model = new class(['id' => 1]) extends \Model {
            private $sanitized = false;

            public function get($key)
            {
                if ($key === 'id') {
                    return 1;
                }
                if ($key === 'purchases') {
                    return [
                        'type' => 'grouping',
                        'names' => ['item', 'price'],
                        'labels' => ['Item', 'Price'],
                        'hidden' => [],
                        'values' => [
                            ['Laptop', 999.99],
                            ['Mouse', 29.99]
                        ]
                    ];
                }
                return parent::get($key);
            }

            public function getExportVarValueWithArgs($varName)
            {
                if ($varName === 'id') {
                    return 1;
                }
                if ($varName === 'name') {
                    return 'Test User';
                }
                return null;
            }

            public function getExportVarType($varName)
            {
                return ExportValue::TYPE_STRING;
            }

            public function checkForVariables()
            {
                return true; // This mock model has variables
            }

            public function getVarsForTemplateAssoc($force = false, $get_gt2_plain_vars = true)
            {
                return [
                    'purchases' => [
                        'type' => 'grouping',
                        'names' => ['item', 'price'],
                        'labels' => ['Item', 'Price'],
                        'hidden' => [],
                        'values' => [
                            ['Laptop', 999.99],
                            ['Mouse', 29.99]
                        ]
                    ]
                ];
            }

            public function isSanitized()
            {
                return $this->sanitized;
            }

            public function unsanitize()
            {
                $this->sanitized = false;
                return $this;
            }

            public function sanitize()
            {
                $this->sanitized = true;
                return $this;
            }
        };

        // Configure table provider
        $this->factory->withModelTableProvider('full_num', 'Document Number', [
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y'
        ]);

        $exportData = $this->factory->__invoke([$model], $this->mockOutlook);

        $this->assertEquals(1, $exportData->count());

        // Check if the record has tables
        $record = $exportData->getRecordAt(0);
        $this->assertNotNull($record);

        // The record should have tables if the model has grouping variables
        // Note: This depends on the ModelTableProvider finding the 'purchases' variable
        if ($record->hasTables()) {
            $tableCollection = $record->getTableCollection();
            $this->assertNotNull($tableCollection);
            $this->assertTrue($tableCollection->hasTables());
        }
    }

    public function testTableProviderWithStreamingMethods(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Configure table provider
        $this->factory->withModelTableProvider('full_num', 'Document Number', ['include_empty_tables' => true]);

        // Test with createStreaming
        $streamingData = $this->factory->createStreaming('TestFactory', [], $this->mockOutlook, 100);
        $this->assertInstanceOf(ExportData::class, $streamingData);
        $this->assertTrue($streamingData->isLazy());

        // Test with createCursorStreaming
        $cursorData = $this->factory->createCursorStreaming('TestModel', [], $this->mockOutlook, 100, 'id');
        $this->assertInstanceOf(ExportData::class, $cursorData);
        $this->assertTrue($cursorData->isLazy());

        // Table provider should still be enabled
        $this->assertTrue($this->factory->isTablesEnabled());
    }

    public function testTableProviderErrorHandling(): void
    {
        $fields = [
            [
                'name' => 'id',
                'label' => 'ID',
                'field_type' => 'text',
                'position' => '1'
            ]
        ];

        $this->mockOutlook->setFields($fields);

        // Create a mock model that will cause table extraction to fail
        $model = new class(['id' => 1]) extends \Model {
            private $sanitized = false;

            public function get($key)
            {
                if ($key === 'id') {
                    return 1;
                }
                if ($key === 'purchases') {
                    throw new \Exception('Database error');
                }
                return parent::get($key);
            }

            public function getExportVarValueWithArgs($varName)
            {
                if ($varName === 'id') {
                    return 1;
                }
                return null;
            }

            public function getExportVarType($varName)
            {
                return ExportValue::TYPE_STRING;
            }

            public function checkForVariables()
            {
                return true; // This mock model has variables
            }

            public function getVarsForTemplateAssoc($force = false, $get_gt2_plain_vars = true)
            {
                // This will trigger the exception when ModelTableProvider tries to access 'purchases'
                return [
                    'purchases' => $this->get('purchases') // This will throw the exception
                ];
            }

            public function isSanitized()
            {
                return $this->sanitized;
            }

            public function unsanitize()
            {
                $this->sanitized = false;
                return $this;
            }

            public function sanitize()
            {
                $this->sanitized = true;
                return $this;
            }
        };

        // Configure table provider
        $this->factory->withModelTableProvider('full_num', 'Document Number');

        // Export should still work even if table extraction fails
        $exportData = $this->factory->__invoke([$model], $this->mockOutlook);

        $this->assertEquals(1, $exportData->count());
        $record = $exportData->getRecordAt(0);
        $this->assertNotNull($record);

        // Record should not have tables due to the error
        $this->assertFalse($record->hasTables());
    }

    public function testMultipleTableProviderConfigurations(): void
    {
        // Test that we can reconfigure the table provider
        $this->factory->withModelTableProvider('full_num', 'Document Number', ['include_empty_tables' => false]);
        $this->assertTrue($this->factory->isTablesEnabled());

        // Reconfigure with different options
        $this->factory->withModelTableProvider('customer_id', 'Customer ID', ['include_empty_tables' => true]);
        $this->assertTrue($this->factory->isTablesEnabled());

        // Disable by setting null provider
        $this->factory->setTableProvider(null);
        $this->assertFalse($this->factory->isTablesEnabled());

        // Re-enable
        $this->factory->withModelTableProvider('full_num', 'Document Number');
        $this->assertTrue($this->factory->isTablesEnabled());
    }
}
