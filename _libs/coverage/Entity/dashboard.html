<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Entity</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Entity</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">9%</td></tr>
       <tr><td><a href="ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">18%</td></tr>
       <tr><td><a href="ExportTable.php.html#11">Nzoom\Export\Entity\ExportTable</a></td><td class="text-right">26%</td></tr>
       <tr><td><a href="ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">29%</td></tr>
       <tr><td><a href="ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">37%</td></tr>
       <tr><td><a href="ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">39%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">2070</td></tr>
       <tr><td><a href="ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">855</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#11">Nzoom\Export\Entity\ExportTableCollection</a></td><td class="text-right">801</td></tr>
       <tr><td><a href="ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">461</td></tr>
       <tr><td><a href="ExportTable.php.html#11">Nzoom\Export\Entity\ExportTable</a></td><td class="text-right">390</td></tr>
       <tr><td><a href="ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">275</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#169"><abbr title="Nzoom\Export\Entity\ExportTable::setRecords">setRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#266"><abbr title="Nzoom\Export\Entity\ExportTable::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#248"><abbr title="Nzoom\Export\Entity\ExportTable::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#238"><abbr title="Nzoom\Export\Entity\ExportTable::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#227"><abbr title="Nzoom\Export\Entity\ExportTable::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#215"><abbr title="Nzoom\Export\Entity\ExportTable::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#205"><abbr title="Nzoom\Export\Entity\ExportTable::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#185"><abbr title="Nzoom\Export\Entity\ExportTable::clearRecords">clearRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#157"><abbr title="Nzoom\Export\Entity\ExportTable::getRecords">getRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#59"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#131"><abbr title="Nzoom\Export\Entity\ExportTable::setParentReference">setParentReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#121"><abbr title="Nzoom\Export\Entity\ExportTable::getParentReference">getParentReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#111"><abbr title="Nzoom\Export\Entity\ExportTable::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#101"><abbr title="Nzoom\Export\Entity\ExportTable::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#91"><abbr title="Nzoom\Export\Entity\ExportTable::setTableName">setTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTable::getTableName">getTableName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#349"><abbr title="Nzoom\Export\Entity\ExportRecord::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#339"><abbr title="Nzoom\Export\Entity\ExportRecord::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTable.php.html#296"><abbr title="Nzoom\Export\Entity\ExportTable::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#311"><abbr title="Nzoom\Export\Entity\ExportRecord::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportValue.php.html#153"><abbr title="Nzoom\Export\Entity\ExportValue::isNull">isNull</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportValue.php.html#142"><abbr title="Nzoom\Export\Entity\ExportValue::setFormat">setFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportValue.php.html#131"><abbr title="Nzoom\Export\Entity\ExportValue::getFormat">getFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#240"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#230"><abbr title="Nzoom\Export\Entity\ExportTableCollection::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#96"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTables">getTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#190"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#179"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#167"><abbr title="Nzoom\Export\Entity\ExportTableCollection::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#157"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#146"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypesWithRecords">getTableTypesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#134"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTablesWithRecords">getTablesWithRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#114"><abbr title="Nzoom\Export\Entity\ExportTableCollection::clearTables">clearTables</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#106"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTableTypes">getTableTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#321"><abbr title="Nzoom\Export\Entity\ExportRecord::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#331"><abbr title="Nzoom\Export\Entity\ExportRecord::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#185"><abbr title="Nzoom\Export\Entity\ExportData::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#407"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#387"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#369"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#358"><abbr title="Nzoom\Export\Entity\ExportData::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#310"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#294"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#278"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#174"><abbr title="Nzoom\Export\Entity\ExportData::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#162"><abbr title="Nzoom\Export\Entity\ExportData::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#142"><abbr title="Nzoom\Export\Entity\ExportData::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#122"><abbr title="Nzoom\Export\Entity\ExportData::getModelType">getModelType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportData.php.html#111"><abbr title="Nzoom\Export\Entity\ExportData::setPageSize">setPageSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportColumn.php.html#235"><abbr title="Nzoom\Export\Entity\ExportColumn::createValue">createValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportColumn.php.html#212"><abbr title="Nzoom\Export\Entity\ExportColumn::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportColumn.php.html#191"><abbr title="Nzoom\Export\Entity\ExportColumn::getStyles">getStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#89"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#100"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnAt">getColumnAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#135"><abbr title="Nzoom\Export\Entity\ExportHeader::setBackgroundColor">setBackgroundColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#192"><abbr title="Nzoom\Export\Entity\ExportRecord::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#180"><abbr title="Nzoom\Export\Entity\ExportRecord::getFormattedValues">getFormattedValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#168"><abbr title="Nzoom\Export\Entity\ExportRecord::getRawValues">getRawValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#202"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#121"><abbr title="Nzoom\Export\Entity\ExportRecord::getValues">getValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#103"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#225"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportRecord.php.html#76"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#311"><abbr title="Nzoom\Export\Entity\ExportHeader::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#210"><abbr title="Nzoom\Export\Entity\ExportHeader::getTypes">getTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#186"><abbr title="Nzoom\Export\Entity\ExportHeader::getLabels">getLabels</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#166"><abbr title="Nzoom\Export\Entity\ExportHeader::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportHeader.php.html#155"><abbr title="Nzoom\Export\Entity\ExportHeader::setStyles">setStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">42%</td></tr>
       <tr><td><a href="ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="ExportData.php.html#199"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="ExportData.php.html#261"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="ExportRecord.php.html#143"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="ExportTable.php.html#143"><abbr title="Nzoom\Export\Entity\ExportTable::addRecord">addRecord</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="ExportHeader.php.html#264"><abbr title="Nzoom\Export\Entity\ExportHeader::validateRecord">validateRecord</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">400</td></tr>
       <tr><td><a href="ExportData.php.html#310"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#274"><abbr title="Nzoom\Export\Entity\ExportTableCollection::fromArray">fromArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ExportTable.php.html#169"><abbr title="Nzoom\Export\Entity\ExportTable::setRecords">setRecords</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportRecord.php.html#76"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportData.php.html#369"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportData.php.html#387"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#200"><abbr title="Nzoom\Export\Entity\ExportTableCollection::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportTable.php.html#266"><abbr title="Nzoom\Export\Entity\ExportTable::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportTable.php.html#248"><abbr title="Nzoom\Export\Entity\ExportTable::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#252"><abbr title="Nzoom\Export\Entity\ExportTableCollection::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#216"><abbr title="Nzoom\Export\Entity\ExportTableCollection::getTotalRecordCount">getTotalRecordCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#81"><abbr title="Nzoom\Export\Entity\ExportTableCollection::removeTable">removeTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportData.php.html#294"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportRecord.php.html#276"><abbr title="Nzoom\Export\Entity\ExportRecord::addTable">addTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportRecord.php.html#103"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportData.php.html#278"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportData.php.html#407"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportRecord.php.html#266"><abbr title="Nzoom\Export\Entity\ExportRecord::getTable">getTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportHeader.php.html#264"><abbr title="Nzoom\Export\Entity\ExportHeader::validateRecord">validateRecord</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="ExportData.php.html#199"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="ExportTable.php.html#143"><abbr title="Nzoom\Export\Entity\ExportTable::addRecord">addRecord</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="ExportTableCollection.php.html#39"><abbr title="Nzoom\Export\Entity\ExportTableCollection::addTable">addTable</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="ExportData.php.html#261"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="ExportRecord.php.html#143"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Tue Jun 24 14:36:19 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,1,1,2,2,0,0,0,1,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([78,0,0,3,0,1,1,7,0,4,0,48], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[70,18,"<a href=\"ExportColumn.php.html#10\">Nzoom\\Export\\Entity\\ExportColumn<\/a>"],[37.37373737373738,57,"<a href=\"ExportData.php.html#12\">Nzoom\\Export\\Entity\\ExportData<\/a>"],[39.39393939393939,33,"<a href=\"ExportHeader.php.html#11\">Nzoom\\Export\\Entity\\ExportHeader<\/a>"],[29.629629629629626,35,"<a href=\"ExportRecord.php.html#11\">Nzoom\\Export\\Entity\\ExportRecord<\/a>"],[26.31578947368421,30,"<a href=\"ExportTable.php.html#11\">Nzoom\\Export\\Entity\\ExportTable<\/a>"],[9.090909090909092,32,"<a href=\"ExportTableCollection.php.html#11\">Nzoom\\Export\\Entity\\ExportTableCollection<\/a>"],[18.571428571428573,61,"<a href=\"ExportValue.php.html#10\">Nzoom\\Export\\Entity\\ExportValue<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"ExportColumn.php.html#53\">Nzoom\\Export\\Entity\\ExportColumn::__construct<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#74\">Nzoom\\Export\\Entity\\ExportColumn::getVarName<\/a>"],[66.66666666666666,2,"<a href=\"ExportColumn.php.html#86\">Nzoom\\Export\\Entity\\ExportColumn::setVarName<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#100\">Nzoom\\Export\\Entity\\ExportColumn::getLabel<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#110\">Nzoom\\Export\\Entity\\ExportColumn::setLabel<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#120\">Nzoom\\Export\\Entity\\ExportColumn::getType<\/a>"],[28.57142857142857,2,"<a href=\"ExportColumn.php.html#131\">Nzoom\\Export\\Entity\\ExportColumn::setType<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#150\">Nzoom\\Export\\Entity\\ExportColumn::getFormat<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#161\">Nzoom\\Export\\Entity\\ExportColumn::setFormat<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#171\">Nzoom\\Export\\Entity\\ExportColumn::getWidth<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#181\">Nzoom\\Export\\Entity\\ExportColumn::setWidth<\/a>"],[0,1,"<a href=\"ExportColumn.php.html#191\">Nzoom\\Export\\Entity\\ExportColumn::getStyles<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#201\">Nzoom\\Export\\Entity\\ExportColumn::setStyles<\/a>"],[0,1,"<a href=\"ExportColumn.php.html#212\">Nzoom\\Export\\Entity\\ExportColumn::addStyle<\/a>"],[100,1,"<a href=\"ExportColumn.php.html#223\">Nzoom\\Export\\Entity\\ExportColumn::validateValue<\/a>"],[0,1,"<a href=\"ExportColumn.php.html#235\">Nzoom\\Export\\Entity\\ExportColumn::createValue<\/a>"],[100,1,"<a href=\"ExportData.php.html#58\">Nzoom\\Export\\Entity\\ExportData::__construct<\/a>"],[100,1,"<a href=\"ExportData.php.html#73\">Nzoom\\Export\\Entity\\ExportData::setRecordProvider<\/a>"],[100,1,"<a href=\"ExportData.php.html#90\">Nzoom\\Export\\Entity\\ExportData::isLazy<\/a>"],[100,1,"<a href=\"ExportData.php.html#100\">Nzoom\\Export\\Entity\\ExportData::getPageSize<\/a>"],[0,1,"<a href=\"ExportData.php.html#111\">Nzoom\\Export\\Entity\\ExportData::setPageSize<\/a>"],[0,1,"<a href=\"ExportData.php.html#122\">Nzoom\\Export\\Entity\\ExportData::getModelType<\/a>"],[100,1,"<a href=\"ExportData.php.html#132\">Nzoom\\Export\\Entity\\ExportData::getHeader<\/a>"],[0,1,"<a href=\"ExportData.php.html#142\">Nzoom\\Export\\Entity\\ExportData::setHeader<\/a>"],[100,1,"<a href=\"ExportData.php.html#152\">Nzoom\\Export\\Entity\\ExportData::getMetadata<\/a>"],[0,1,"<a href=\"ExportData.php.html#162\">Nzoom\\Export\\Entity\\ExportData::setMetadata<\/a>"],[0,1,"<a href=\"ExportData.php.html#174\">Nzoom\\Export\\Entity\\ExportData::getMetadataValue<\/a>"],[0,1,"<a href=\"ExportData.php.html#185\">Nzoom\\Export\\Entity\\ExportData::setMetadataValue<\/a>"],[60,4,"<a href=\"ExportData.php.html#199\">Nzoom\\Export\\Entity\\ExportData::addRecord<\/a>"],[83.33333333333334,3,"<a href=\"ExportData.php.html#218\">Nzoom\\Export\\Entity\\ExportData::getRecords<\/a>"],[87.5,4,"<a href=\"ExportData.php.html#239\">Nzoom\\Export\\Entity\\ExportData::getRecordAt<\/a>"],[66.66666666666666,2,"<a href=\"ExportData.php.html#261\">Nzoom\\Export\\Entity\\ExportData::count<\/a>"],[0,2,"<a href=\"ExportData.php.html#278\">Nzoom\\Export\\Entity\\ExportData::createRecord<\/a>"],[0,2,"<a href=\"ExportData.php.html#294\">Nzoom\\Export\\Entity\\ExportData::isEmpty<\/a>"],[0,15,"<a href=\"ExportData.php.html#310\">Nzoom\\Export\\Entity\\ExportData::sortByColumn<\/a>"],[0,1,"<a href=\"ExportData.php.html#358\">Nzoom\\Export\\Entity\\ExportData::filter<\/a>"],[0,3,"<a href=\"ExportData.php.html#369\">Nzoom\\Export\\Entity\\ExportData::validate<\/a>"],[0,3,"<a href=\"ExportData.php.html#387\">Nzoom\\Export\\Entity\\ExportData::toArray<\/a>"],[0,2,"<a href=\"ExportData.php.html#407\">Nzoom\\Export\\Entity\\ExportData::getIterator<\/a>"],[80,4,"<a href=\"ExportData.php.html#423\">Nzoom\\Export\\Entity\\ExportData::getLazyIterator<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#44\">Nzoom\\Export\\Entity\\ExportHeader::__construct<\/a>"],[50,2,"<a href=\"ExportHeader.php.html#57\">Nzoom\\Export\\Entity\\ExportHeader::addColumn<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#79\">Nzoom\\Export\\Entity\\ExportHeader::hasColumn<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#89\">Nzoom\\Export\\Entity\\ExportHeader::getColumns<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#100\">Nzoom\\Export\\Entity\\ExportHeader::getColumnAt<\/a>"],[66.66666666666666,2,"<a href=\"ExportHeader.php.html#111\">Nzoom\\Export\\Entity\\ExportHeader::getColumnByVarName<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#125\">Nzoom\\Export\\Entity\\ExportHeader::getBackgroundColor<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#135\">Nzoom\\Export\\Entity\\ExportHeader::setBackgroundColor<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#145\">Nzoom\\Export\\Entity\\ExportHeader::getStyles<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#155\">Nzoom\\Export\\Entity\\ExportHeader::setStyles<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#166\">Nzoom\\Export\\Entity\\ExportHeader::addStyle<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#176\">Nzoom\\Export\\Entity\\ExportHeader::count<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#186\">Nzoom\\Export\\Entity\\ExportHeader::getLabels<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#198\">Nzoom\\Export\\Entity\\ExportHeader::getVarNames<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#210\">Nzoom\\Export\\Entity\\ExportHeader::getTypes<\/a>"],[0,6,"<a href=\"ExportHeader.php.html#223\">Nzoom\\Export\\Entity\\ExportHeader::reorderColumns<\/a>"],[66.66666666666666,5,"<a href=\"ExportHeader.php.html#264\">Nzoom\\Export\\Entity\\ExportHeader::validateRecord<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#291\">Nzoom\\Export\\Entity\\ExportHeader::rewind<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#301\">Nzoom\\Export\\Entity\\ExportHeader::current<\/a>"],[0,1,"<a href=\"ExportHeader.php.html#311\">Nzoom\\Export\\Entity\\ExportHeader::key<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#319\">Nzoom\\Export\\Entity\\ExportHeader::next<\/a>"],[100,1,"<a href=\"ExportHeader.php.html#329\">Nzoom\\Export\\Entity\\ExportHeader::valid<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#43\">Nzoom\\Export\\Entity\\ExportRecord::__construct<\/a>"],[80,2,"<a href=\"ExportRecord.php.html#57\">Nzoom\\Export\\Entity\\ExportRecord::addValue<\/a>"],[0,4,"<a href=\"ExportRecord.php.html#76\">Nzoom\\Export\\Entity\\ExportRecord::setValueAt<\/a>"],[0,2,"<a href=\"ExportRecord.php.html#103\">Nzoom\\Export\\Entity\\ExportRecord::setValueByColumnName<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#121\">Nzoom\\Export\\Entity\\ExportRecord::getValues<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#132\">Nzoom\\Export\\Entity\\ExportRecord::getValueAt<\/a>"],[66.66666666666666,2,"<a href=\"ExportRecord.php.html#143\">Nzoom\\Export\\Entity\\ExportRecord::getValueByColumnName<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#158\">Nzoom\\Export\\Entity\\ExportRecord::hasValue<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#168\">Nzoom\\Export\\Entity\\ExportRecord::getRawValues<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#180\">Nzoom\\Export\\Entity\\ExportRecord::getFormattedValues<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#192\">Nzoom\\Export\\Entity\\ExportRecord::getMetadata<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#202\">Nzoom\\Export\\Entity\\ExportRecord::setMetadata<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#214\">Nzoom\\Export\\Entity\\ExportRecord::getMetadataValue<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#225\">Nzoom\\Export\\Entity\\ExportRecord::setMetadataValue<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#235\">Nzoom\\Export\\Entity\\ExportRecord::getTableCollection<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#245\">Nzoom\\Export\\Entity\\ExportRecord::setTableCollection<\/a>"],[100,2,"<a href=\"ExportRecord.php.html#255\">Nzoom\\Export\\Entity\\ExportRecord::hasTables<\/a>"],[0,2,"<a href=\"ExportRecord.php.html#266\">Nzoom\\Export\\Entity\\ExportRecord::getTable<\/a>"],[0,2,"<a href=\"ExportRecord.php.html#276\">Nzoom\\Export\\Entity\\ExportRecord::addTable<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#291\">Nzoom\\Export\\Entity\\ExportRecord::validate<\/a>"],[100,1,"<a href=\"ExportRecord.php.html#301\">Nzoom\\Export\\Entity\\ExportRecord::count<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#311\">Nzoom\\Export\\Entity\\ExportRecord::rewind<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#321\">Nzoom\\Export\\Entity\\ExportRecord::current<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#331\">Nzoom\\Export\\Entity\\ExportRecord::key<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#339\">Nzoom\\Export\\Entity\\ExportRecord::next<\/a>"],[0,1,"<a href=\"ExportRecord.php.html#349\">Nzoom\\Export\\Entity\\ExportRecord::valid<\/a>"],[100,1,"<a href=\"ExportTable.php.html#52\">Nzoom\\Export\\Entity\\ExportTable::__construct<\/a>"],[100,1,"<a href=\"ExportTable.php.html#71\">Nzoom\\Export\\Entity\\ExportTable::getTableType<\/a>"],[0,1,"<a href=\"ExportTable.php.html#81\">Nzoom\\Export\\Entity\\ExportTable::getTableName<\/a>"],[0,1,"<a href=\"ExportTable.php.html#91\">Nzoom\\Export\\Entity\\ExportTable::setTableName<\/a>"],[0,1,"<a href=\"ExportTable.php.html#101\">Nzoom\\Export\\Entity\\ExportTable::getHeader<\/a>"],[0,1,"<a href=\"ExportTable.php.html#111\">Nzoom\\Export\\Entity\\ExportTable::setHeader<\/a>"],[0,1,"<a href=\"ExportTable.php.html#121\">Nzoom\\Export\\Entity\\ExportTable::getParentReference<\/a>"],[0,1,"<a href=\"ExportTable.php.html#131\">Nzoom\\Export\\Entity\\ExportTable::setParentReference<\/a>"],[66.66666666666666,3,"<a href=\"ExportTable.php.html#143\">Nzoom\\Export\\Entity\\ExportTable::addRecord<\/a>"],[0,1,"<a href=\"ExportTable.php.html#157\">Nzoom\\Export\\Entity\\ExportTable::getRecords<\/a>"],[0,4,"<a href=\"ExportTable.php.html#169\">Nzoom\\Export\\Entity\\ExportTable::setRecords<\/a>"],[0,1,"<a href=\"ExportTable.php.html#185\">Nzoom\\Export\\Entity\\ExportTable::clearRecords<\/a>"],[100,1,"<a href=\"ExportTable.php.html#195\">Nzoom\\Export\\Entity\\ExportTable::hasRecords<\/a>"],[0,1,"<a href=\"ExportTable.php.html#205\">Nzoom\\Export\\Entity\\ExportTable::getMetadata<\/a>"],[0,1,"<a href=\"ExportTable.php.html#215\">Nzoom\\Export\\Entity\\ExportTable::setMetadata<\/a>"],[0,1,"<a href=\"ExportTable.php.html#227\">Nzoom\\Export\\Entity\\ExportTable::getMetadataValue<\/a>"],[0,1,"<a href=\"ExportTable.php.html#238\">Nzoom\\Export\\Entity\\ExportTable::setMetadataValue<\/a>"],[0,3,"<a href=\"ExportTable.php.html#248\">Nzoom\\Export\\Entity\\ExportTable::validate<\/a>"],[0,3,"<a href=\"ExportTable.php.html#266\">Nzoom\\Export\\Entity\\ExportTable::toArray<\/a>"],[100,1,"<a href=\"ExportTable.php.html#286\">Nzoom\\Export\\Entity\\ExportTable::count<\/a>"],[0,1,"<a href=\"ExportTable.php.html#296\">Nzoom\\Export\\Entity\\ExportTable::getIterator<\/a>"],[100,1,"<a href=\"ExportTableCollection.php.html#28\">Nzoom\\Export\\Entity\\ExportTableCollection::__construct<\/a>"],[42.857142857142854,2,"<a href=\"ExportTableCollection.php.html#39\">Nzoom\\Export\\Entity\\ExportTableCollection::addTable<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#59\">Nzoom\\Export\\Entity\\ExportTableCollection::getTable<\/a>"],[100,1,"<a href=\"ExportTableCollection.php.html#70\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTable<\/a>"],[0,2,"<a href=\"ExportTableCollection.php.html#81\">Nzoom\\Export\\Entity\\ExportTableCollection::removeTable<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#96\">Nzoom\\Export\\Entity\\ExportTableCollection::getTables<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#106\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypes<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#114\">Nzoom\\Export\\Entity\\ExportTableCollection::clearTables<\/a>"],[100,1,"<a href=\"ExportTableCollection.php.html#124\">Nzoom\\Export\\Entity\\ExportTableCollection::hasTables<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#134\">Nzoom\\Export\\Entity\\ExportTableCollection::getTablesWithRecords<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#146\">Nzoom\\Export\\Entity\\ExportTableCollection::getTableTypesWithRecords<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#157\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadata<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#167\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadata<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#179\">Nzoom\\Export\\Entity\\ExportTableCollection::getMetadataValue<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#190\">Nzoom\\Export\\Entity\\ExportTableCollection::setMetadataValue<\/a>"],[0,3,"<a href=\"ExportTableCollection.php.html#200\">Nzoom\\Export\\Entity\\ExportTableCollection::validate<\/a>"],[0,2,"<a href=\"ExportTableCollection.php.html#216\">Nzoom\\Export\\Entity\\ExportTableCollection::getTotalRecordCount<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#230\">Nzoom\\Export\\Entity\\ExportTableCollection::count<\/a>"],[0,1,"<a href=\"ExportTableCollection.php.html#240\">Nzoom\\Export\\Entity\\ExportTableCollection::getIterator<\/a>"],[0,2,"<a href=\"ExportTableCollection.php.html#252\">Nzoom\\Export\\Entity\\ExportTableCollection::toArray<\/a>"],[0,6,"<a href=\"ExportTableCollection.php.html#274\">Nzoom\\Export\\Entity\\ExportTableCollection::fromArray<\/a>"],[100,1,"<a href=\"ExportValue.php.html#58\">Nzoom\\Export\\Entity\\ExportValue::getValidTypes<\/a>"],[100,1,"<a href=\"ExportValue.php.html#70\">Nzoom\\Export\\Entity\\ExportValue::__construct<\/a>"],[100,1,"<a href=\"ExportValue.php.html#82\">Nzoom\\Export\\Entity\\ExportValue::getValue<\/a>"],[100,1,"<a href=\"ExportValue.php.html#92\">Nzoom\\Export\\Entity\\ExportValue::setValue<\/a>"],[100,1,"<a href=\"ExportValue.php.html#102\">Nzoom\\Export\\Entity\\ExportValue::getType<\/a>"],[28.57142857142857,3,"<a href=\"ExportValue.php.html#113\">Nzoom\\Export\\Entity\\ExportValue::setType<\/a>"],[0,1,"<a href=\"ExportValue.php.html#131\">Nzoom\\Export\\Entity\\ExportValue::getFormat<\/a>"],[0,1,"<a href=\"ExportValue.php.html#142\">Nzoom\\Export\\Entity\\ExportValue::setFormat<\/a>"],[0,1,"<a href=\"ExportValue.php.html#153\">Nzoom\\Export\\Entity\\ExportValue::isNull<\/a>"],[20,27,"<a href=\"ExportValue.php.html#163\">Nzoom\\Export\\Entity\\ExportValue::validate<\/a>"],[0,15,"<a href=\"ExportValue.php.html#196\">Nzoom\\Export\\Entity\\ExportValue::getFormattedValue<\/a>"],[0,8,"<a href=\"ExportValue.php.html#233\">Nzoom\\Export\\Entity\\ExportValue::__toString<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
